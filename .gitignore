# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
.pybuilder/
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
#   For a library or package, you might want to ignore these files since the code is
#   intended to run in multiple environments; otherwise, check them in:
# .python-version

# pipenv
#   According to pypa/pipenv#598, it is recommended to include Pipfile.lock in version control.
#   However, in case of collaboration, if having platform-specific dependencies or dependencies
#   having no cross-platform support, pipenv may install dependencies that don't work, or not
#   install all needed dependencies.
#Pipfile.lock

# poetry
#   Similar to Pipfile.lock, it is generally recommended to include poetry.lock in version control.
#   This is especially recommended for binary packages to ensure reproducibility, and is more
#   commonly ignored for libraries.
#   https://python-poetry.org/docs/basic-usage/#commit-your-poetrylock-file-to-version-control
#poetry.lock

# pdm
#   Similar to Pipfile.lock, it is generally recommended to include pdm.lock in version control.
#pdm.lock
#   pdm stores project-wide configurations in .pdm.toml, but it is recommended to not include it
#   in version control.
#   https://pdm.fming.dev/#use-with-ide
.pdm.toml

# PEP 582; used by e.g. github.com/David-OConnor/pyflow and github.com/pdm-project/pdm
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# PyCharm
#  JetBrains specific template is maintained in a separate JetBrains.gitignore that can
#  be added to the global gitignore or merged into this project gitignore.  For a PyCharm
#  project, it is recommended to include the .idea directory in version control.
.idea/

# VS Code
.vscode/
*.code-workspace

# Machine Learning / AI specific
# Model files
*.h5
*.hdf5
*.pkl
*.pickle
*.joblib
*.model
*.pt
*.pth
*.ckpt
*.pb
*.tflite
*.onnx
*.safetensors

# Dataset files
*.csv
*.tsv
*.json
*.jsonl
*.parquet
*.feather
*.arrow
*.hdf
*.mat
*.npz
*.npy

# Large data directories
data/
datasets/
raw_data/
processed_data/
train_data/
test_data/
validation_data/
images/
videos/
audio/

# Model outputs and experiments
models/
checkpoints/
saved_models/
experiments/
runs/
outputs/
results/
logs/
wandb/
mlruns/
.mlflow/

# TensorBoard logs
tensorboard_logs/
tb_logs/
events.out.tfevents.*

# Weights & Biases
wandb/

# MLflow
mlruns/
.mlflow/

# DVC (Data Version Control)
.dvc/
*.dvc

# Jupyter notebook outputs
*.ipynb
!*_template.ipynb
!*_example.ipynb

# Temporary files
tmp/
temp/
cache/
.cache/

# OS specific
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# GPU/CUDA cache
.nv/

# Annotation tools
*.xml
*.txt
!requirements.txt
!README.txt

# Video/Image processing temp files
*.mp4
*.avi
*.mov
*.mkv
*.webm
*.jpg
*.jpeg
*.png
*.gif
*.bmp
*.tiff
*.tif
*.svg
*.webp

# Audio files
*.wav
*.mp3
*.flac
*.aac
*.ogg
*.m4a

# Compressed files
*.zip
*.tar
*.tar.gz
*.rar
*.7z

# Configuration files with sensitive data
config.ini
secrets.json
.secrets
credentials.json
api_keys.txt

# UV specific
.python-version
uv.lock