DATASET_PATHS = [

    dict(
        real_path='/Youtu_Pangu_Security_Public/etoilefu/CNNDetection/dataset/test/progan',     
        fake_path='/Youtu_Pangu_Security_Public/etoilefu/CNNDetection/dataset/test/progan',
        data_mode='wang2020',
        key='progan'
    ),

    dict(
        real_path='/Youtu_Pangu_Security_Public/etoilefu/CNNDetection/dataset/test/cyclegan',   
        fake_path='/Youtu_Pangu_Security_Public/etoilefu/CNNDetection/dataset/test/cyclegan',
        data_mode='wang2020',
        key='cyclegan'
    ),

    dict(
        real_path='/Youtu_Pangu_Security_Public/etoilefu/CNNDetection/dataset/test/biggan/',   # Imagenet 
        fake_path='/Youtu_Pangu_Security_Public/etoilefu/CNNDetection/dataset/test/biggan/',
        data_mode='wang2020',
        key='biggan'
    ),

    dict(
        real_path='/Youtu_Pangu_Security_Public/etoilefu/CNNDetection/dataset/test/stylegan',    
        fake_path='/Youtu_Pangu_Security_Public/etoilefu/CNNDetection/dataset/test/stylegan',
        data_mode='wang2020',
        key='stylegan'
    ),

    dict(
        real_path='/Youtu_Pangu_Security_Public/etoilefu/CNNDetection/dataset/test/gaugan',    # It is COCO 
        fake_path='/Youtu_Pangu_Security_Public/etoilefu/CNNDetection/dataset/test/gaugan',
        data_mode='wang2020',
        key='gaugan'
    ),

    dict(
        real_path='/Youtu_Pangu_Security_Public/etoilefu/CNNDetection/dataset/test/stargan',  
        fake_path='/Youtu_Pangu_Security_Public/etoilefu/CNNDetection/dataset/test/stargan',
        data_mode='wang2020',
        key='stargan'
    ),

    dict(
        real_path='/Youtu_Pangu_Security_Public/etoilefu/CNNDetection/dataset/test/deepfake',   
        fake_path='/Youtu_Pangu_Security_Public/etoilefu/CNNDetection/dataset/test/deepfake',
        data_mode='wang2020',
        key='deepfake'
    ),

    dict(
        real_path='/Youtu_Pangu_Security_Public/etoilefu/CNNDetection/dataset/test/seeingdark',   
        fake_path='/Youtu_Pangu_Security_Public/etoilefu/CNNDetection/dataset/test/seeingdark',
        data_mode='wang2020',
        key='sitd'
    ),

    dict(
        real_path='/Youtu_Pangu_Security_Public/etoilefu/CNNDetection/dataset/test/san',   
        fake_path='/Youtu_Pangu_Security_Public/etoilefu/CNNDetection/dataset/test/san',
        data_mode='wang2020',
        key='san'
    ),

    dict(
        real_path='/Youtu_Pangu_Security_Public/etoilefu/CNNDetection/dataset/test/crn',   # Images from some video games
        fake_path='/Youtu_Pangu_Security_Public/etoilefu/CNNDetection/dataset/test/crn',
        data_mode='wang2020',
        key='crn'
    ),

    dict(
        real_path='/Youtu_Pangu_Security_Public/etoilefu/CNNDetection/dataset/test/imle',   # Images from some video games
        fake_path='/Youtu_Pangu_Security_Public/etoilefu/CNNDetection/dataset/test/imle',
        data_mode='wang2020',
        key='imle'
    ),

    dict(
        real_path='/Youtu_Pangu_Security/jeremiewang/dataset/open/diffusion_datasets/imagenet',
        fake_path='/Youtu_Pangu_Security/jeremiewang/dataset/open/diffusion_datasets/guided',
        data_mode='wang2020',
        key='guided'
    ),

    dict(
        real_path='/Youtu_Pangu_Security/jeremiewang/dataset/open/diffusion_datasets/laion',
        fake_path='/Youtu_Pangu_Security/jeremiewang/dataset/open/diffusion_datasets/ldm_200',
        data_mode='wang2020',
        key='ldm_200'
    ),

    dict(
        real_path='/Youtu_Pangu_Security/jeremiewang/dataset/open/diffusion_datasets/laion',
        fake_path='/Youtu_Pangu_Security/jeremiewang/dataset/open/diffusion_datasets/ldm_200_cfg',
        data_mode='wang2020',
        key='ldm_200_cfg'
    ),

    dict(
        real_path='/Youtu_Pangu_Security/jeremiewang/dataset/open/diffusion_datasets/laion',
        fake_path='/Youtu_Pangu_Security/jeremiewang/dataset/open/diffusion_datasets/ldm_100',
        data_mode='wang2020',
        key='ldm_100'
     ),

    dict(
        real_path='/Youtu_Pangu_Security/jeremiewang/dataset/open/diffusion_datasets/laion',
        fake_path='/Youtu_Pangu_Security/jeremiewang/dataset/open/diffusion_datasets/glide_100_27',
        data_mode='wang2020',
        key='glide_100_27'
    ),

    dict(
        real_path='/Youtu_Pangu_Security/jeremiewang/dataset/open/diffusion_datasets/laion',
        fake_path='/Youtu_Pangu_Security/jeremiewang/dataset/open/diffusion_datasets/glide_50_27',
        data_mode='wang2020',
        key='glide_50_27'
    ),

    dict(
        real_path='/Youtu_Pangu_Security/jeremiewang/dataset/open/diffusion_datasets/laion',
        fake_path='/Youtu_Pangu_Security/jeremiewang/dataset/open/diffusion_datasets/glide_100_10',
        data_mode='wang2020',
        key='glide_100_10'
    ),

    dict(
        real_path='/Youtu_Pangu_Security/jeremiewang/dataset/open/diffusion_datasets/laion',
        fake_path='/Youtu_Pangu_Security/jeremiewang/dataset/open/diffusion_datasets/dalle',
        data_mode='wang2020',
        key='dalle'
    ),

]
