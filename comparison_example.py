#!/usr/bin/env python3
"""
Example script showing the differences between inference.sh and inference.py

This script demonstrates:
1. How inference.sh works (basic output)
2. How inference.py works (detailed CSV output)
3. The additional analysis capabilities
"""

import os
import subprocess
import pandas as pd
from pathlib import Path

def run_shell_inference(video_path):
    """Run the original inference.sh script"""
    print("=" * 60)
    print("RUNNING ORIGINAL inference.sh")
    print("=" * 60)
    
    cmd = ["bash", "inference.sh", video_path]
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        print("STDOUT:")
        print(result.stdout)
        if result.stderr:
            print("STDERR:")
            print(result.stderr)
        return result.returncode == 0
    except subprocess.TimeoutExpired:
        print("Shell script timed out")
        return False
    except Exception as e:
        print(f"Error running shell script: {e}")
        return False

def run_python_inference(video_path, output_dir="./python_inference_results"):
    """Run the new inference.py script"""
    print("=" * 60)
    print("RUNNING NEW inference.py")
    print("=" * 60)
    
    cmd = ["python", "inference.py", video_path, "--output_dir", output_dir]
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        print("STDOUT:")
        print(result.stdout)
        if result.stderr:
            print("STDERR:")
            print(result.stderr)
        return result.returncode == 0, output_dir
    except subprocess.TimeoutExpired:
        print("Python script timed out")
        return False, None
    except Exception as e:
        print(f"Error running Python script: {e}")
        return False, None

def analyze_csv_results(output_dir, video_name):
    """Analyze the CSV results from inference.py"""
    print("=" * 60)
    print("DETAILED ANALYSIS FROM CSV")
    print("=" * 60)
    
    csv_path = os.path.join(output_dir, f"{video_name}_analysis.csv")
    
    if not os.path.exists(csv_path):
        print(f"CSV file not found: {csv_path}")
        return
    
    try:
        df = pd.read_csv(csv_path)
        
        print(f"Analysis for video: {video_name}")
        print(f"Total frames analyzed: {len(df)}")
        print()
        
        # Classification breakdown
        classification_counts = df['classification'].value_counts()
        print("Classification breakdown:")
        for classification, count in classification_counts.items():
            percentage = (count / len(df)) * 100
            print(f"  {classification}: {count} frames ({percentage:.1f}%)")
        print()
        
        # Score statistics for frames with faces
        face_frames = df[df['classification'] != 'No Face Detected']
        if len(face_frames) > 0:
            print("Model score statistics (frames with faces):")
            print(f"  Mean score: {face_frames['model_score'].mean():.4f}")
            print(f"  Median score: {face_frames['model_score'].median():.4f}")
            print(f"  Min score: {face_frames['model_score'].min():.4f}")
            print(f"  Max score: {face_frames['model_score'].max():.4f}")
            print()
        
        # Timeline analysis
        print("Timeline analysis:")
        fake_frames = df[df['classification'] == 'Fake']
        if len(fake_frames) > 0:
            fake_timestamps = fake_frames['timestamp'].tolist()
            print(f"  Fake content detected at timestamps: {fake_timestamps[:10]}{'...' if len(fake_timestamps) > 10 else ''}")
            
            # Find consecutive fake segments
            consecutive_segments = []
            current_segment = [fake_timestamps[0]]
            
            for i in range(1, len(fake_timestamps)):
                if fake_timestamps[i] == fake_timestamps[i-1] + 1:
                    current_segment.append(fake_timestamps[i])
                else:
                    if len(current_segment) > 1:
                        consecutive_segments.append((current_segment[0], current_segment[-1]))
                    current_segment = [fake_timestamps[i]]
            
            if len(current_segment) > 1:
                consecutive_segments.append((current_segment[0], current_segment[-1]))
            
            if consecutive_segments:
                print(f"  Consecutive fake segments:")
                for start, end in consecutive_segments:
                    print(f"    {start}s - {end}s ({end-start+1} seconds)")
        else:
            print("  No fake content detected")
        
        print()
        
        # Show first few rows
        print("Sample data (first 5 rows):")
        print(df.head().to_string(index=False))
        
    except Exception as e:
        print(f"Error analyzing CSV: {e}")

def main():
    """Main comparison function"""
    # Use a small video for testing
    test_video = "Videos/biden.mp4"  # Adjust this path as needed
    
    if not os.path.exists(test_video):
        print(f"Test video not found: {test_video}")
        print("Available videos:")
        videos_dir = "Videos"
        if os.path.exists(videos_dir):
            for f in os.listdir(videos_dir):
                if f.endswith(('.mp4', '.avi', '.mov', '.mkv')):
                    print(f"  {f}")
        return
    
    video_name = Path(test_video).stem
    
    print("COMPARISON: inference.sh vs inference.py")
    print("=" * 60)
    print(f"Test video: {test_video}")
    print()
    
    # Note: Commenting out shell script run to avoid conflicts
    # In practice, you would run this first
    print("NOTE: Shell script run commented out to avoid conflicts")
    print("The shell script would normally run here and show basic output")
    print()
    
    # Run Python inference
    success, output_dir = run_python_inference(test_video)
    
    if success and output_dir:
        # Analyze results
        analyze_csv_results(output_dir, video_name)
        
        print("=" * 60)
        print("SUMMARY OF DIFFERENCES")
        print("=" * 60)
        print("inference.sh:")
        print("  ✓ Extracts frames at 1FPS")
        print("  ✓ Runs deepfake detection")
        print("  ✓ Shows basic classification per frame")
        print("  ✓ Cleans up temporary files")
        print()
        print("inference.py:")
        print("  ✓ All functionality of inference.sh")
        print("  ✓ Saves detailed results to CSV files")
        print("  ✓ Provides frame-by-frame analysis")
        print("  ✓ Includes model confidence scores")
        print("  ✓ Tracks face detection status")
        print("  ✓ Generates summary statistics")
        print("  ✓ Enables timeline analysis")
        print("  ✓ Machine-readable output for further processing")
        print()
        print(f"CSV results saved in: {output_dir}")
    else:
        print("Python inference failed")

if __name__ == "__main__":
    main()
