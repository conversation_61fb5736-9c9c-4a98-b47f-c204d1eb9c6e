[project]
name = "effort-aigi-detection"
version = "0.1.0"
requires-python = ">=3.10"
dependencies = [
    "albumentations>=2.0.8",
    "clip",
    "dlib>=20.0.0",
    "efficientnet-pytorch>=0.7.1",
    "einops>=0.8.1",
    "filterpy>=1.4.5",
    "fvcore>=0.1.5.post20221221",
    "imageio>=2.37.0",
    "imgaug>=0.4.0",
    "imutils>=0.5.4",
    "kornia>=0.8.1",
    "loralib>=0.1.2",
    "numpy<=2.0",
    "opencv-python>=*********",
    "pandas>=2.3.0",
    "pillow>=11.3.0",
    "plotly>=6.2.0",
    "pyyaml>=6.0.2",
    "scikit-image>=0.25.2",
    "scikit-learn>=1.7.0",
    "scipy>=1.15.3",
    "seaborn>=0.13.2",
    "segmentation-models-pytorch>=0.5.0",
    "setuptools>=80.9.0",
    "simplejson>=3.20.1",
    "streamlit>=1.46.1",
    "tensorboard>=2.19.0",
    "timm>=1.0.16",
    "torch>=2.7.1",
    "torchaudio>=2.7.1",
    "torchtoolbox>=*******",
    "torchvision>=0.22.1",
    "tqdm>=4.67.1",
    "transformers>=4.53.1",
    "watchdog>=6.0.0",
]

[[tool.uv.index]]
name = "pytorch-cu118"
url = "https://download.pytorch.org/whl/cu118"
explicit = true

[tool.uv.sources]
torch = [
  { index = "pytorch-cu118", marker = "sys_platform == 'linux' or sys_platform == 'win32'" },
]
torchvision = [
  { index = "pytorch-cu118", marker = "sys_platform == 'linux' or sys_platform == 'win32'" },
]
clip = { git = "https://github.com/openai/CLIP.git" }
