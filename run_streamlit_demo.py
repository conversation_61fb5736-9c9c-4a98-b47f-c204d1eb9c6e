#!/usr/bin/env python3
"""
Demo script to launch the Streamlit Deepfake Detection UI

This script:
1. Checks dependencies
2. Sets up the environment
3. Launches the Streamlit app
4. Provides usage instructions
"""

import os
import sys
import subprocess
import importlib.util
from pathlib import Path

def check_dependencies():
    """Check if required dependencies are installed"""
    required_packages = [
        'streamlit',
        'plotly',
        'pandas',
        'numpy',
        'cv2',
        'PIL',
        'torch',
        'yaml',
        'dlib'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'cv2':
                import cv2
            elif package == 'PIL':
                import PIL
            else:
                importlib.import_module(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package}")
            missing_packages.append(package)
    
    return missing_packages

def install_streamlit_requirements():
    """Install Streamlit-specific requirements"""
    try:
        print("Installing Streamlit requirements...")
        subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "streamlit_requirements.txt"
        ], check=True)
        print("✅ Streamlit requirements installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install requirements: {e}")
        return False

def check_model_files():
    """Check if required model files exist"""
    required_files = [
        "effort_clip_L14_trainOn_FaceForensic.pth",
        "DeepfakeBench/preprocessing/shape_predictor_81_face_landmarks.dat",
        "DeepfakeBench/training/config/detector/effort.yaml"
    ]
    
    missing_files = []
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path}")
            missing_files.append(file_path)
    
    return missing_files

def create_sample_data():
    """Create sample analysis data for demo purposes"""
    import pandas as pd
    import numpy as np
    from datetime import datetime
    
    # Create sample data directory
    sample_dir = "./streamlit_results/sample_demo"
    os.makedirs(sample_dir, exist_ok=True)
    
    # Generate sample analysis data
    np.random.seed(42)  # For reproducible demo data
    
    # Sample video 1: Mostly real content
    frames_1 = 30
    sample_data_1 = {
        'frame_number': range(1, frames_1 + 1),
        'frame_filename': [f'frame_{i:04d}.jpg' for i in range(1, frames_1 + 1)],
        'model_score': np.random.beta(2, 8, frames_1),  # Skewed towards low scores (real)
        'prediction_label': [0 if score < 0.5 else 1 for score in np.random.beta(2, 8, frames_1)],
        'timestamp': range(1, frames_1 + 1)
    }
    
    # Add classification based on model_score
    sample_data_1['classification'] = ['Real' if score < 0.5 else 'Fake' for score in sample_data_1['model_score']]
    
    # Add some "No Face Detected" frames
    for i in [5, 15, 25]:
        if i < len(sample_data_1['classification']):
            sample_data_1['classification'][i] = 'No Face Detected'
            sample_data_1['model_score'][i] = 0.0
            sample_data_1['prediction_label'][i] = -1
    
    df_1 = pd.DataFrame(sample_data_1)
    df_1.to_csv(os.path.join(sample_dir, "sample_real_video_analysis.csv"), index=False)
    
    # Sample video 2: Mixed content with fake segments
    frames_2 = 40
    sample_data_2 = {
        'frame_number': range(1, frames_2 + 1),
        'frame_filename': [f'frame_{i:04d}.jpg' for i in range(1, frames_2 + 1)],
        'model_score': np.concatenate([
            np.random.beta(2, 8, 15),  # Real segment
            np.random.beta(8, 2, 10),  # Fake segment
            np.random.beta(2, 8, 10),  # Real segment
            np.random.beta(8, 2, 5)    # Fake segment
        ]),
        'timestamp': range(1, frames_2 + 1)
    }
    
    sample_data_2['prediction_label'] = [0 if score < 0.5 else 1 for score in sample_data_2['model_score']]
    sample_data_2['classification'] = ['Real' if score < 0.5 else 'Fake' for score in sample_data_2['model_score']]
    
    df_2 = pd.DataFrame(sample_data_2)
    df_2.to_csv(os.path.join(sample_dir, "sample_mixed_video_analysis.csv"), index=False)
    
    # Sample video 3: Mostly fake content
    frames_3 = 25
    sample_data_3 = {
        'frame_number': range(1, frames_3 + 1),
        'frame_filename': [f'frame_{i:04d}.jpg' for i in range(1, frames_3 + 1)],
        'model_score': np.random.beta(8, 2, frames_3),  # Skewed towards high scores (fake)
        'timestamp': range(1, frames_3 + 1)
    }
    
    sample_data_3['prediction_label'] = [0 if score < 0.5 else 1 for score in sample_data_3['model_score']]
    sample_data_3['classification'] = ['Real' if score < 0.5 else 'Fake' for score in sample_data_3['model_score']]
    
    df_3 = pd.DataFrame(sample_data_3)
    df_3.to_csv(os.path.join(sample_dir, "sample_fake_video_analysis.csv"), index=False)
    
    print(f"✅ Sample data created in {sample_dir}")
    return sample_dir

def print_usage_instructions():
    """Print usage instructions for the Streamlit app"""
    print("\n" + "="*60)
    print("🎭 DEEPFAKE DETECTION STUDIO - USAGE GUIDE")
    print("="*60)
    print("""
📋 FEATURES OVERVIEW:

1. 🏠 HOME PAGE
   - Overview of features and capabilities
   - Sample visualizations
   - Getting started guide

2. 📹 SINGLE VIDEO ANALYSIS
   - Upload individual video files
   - Real-time inference processing
   - Comprehensive analysis dashboard
   - Export results (CSV, JSON)

3. 📊 BATCH ANALYSIS
   - Process multiple videos at once
   - Comparative analysis across videos
   - Batch statistics and insights

4. 🔍 COMPARE VIDEOS
   - Side-by-side comparison of analysis results
   - Relative performance metrics
   - Trend analysis

5. 📈 ADVANCED ANALYTICS
   - Cross-video pattern analysis
   - Statistical insights
   - Distribution analysis

📊 KEY METRICS DISPLAYED:

• Health Score: Overall authenticity percentage (0-100%)
• Face Detection Rate: Percentage of frames with detectable faces
• Model Confidence: Average certainty score (0.0-1.0)
• Fake Segments: Number of consecutive suspicious periods
• Temporal Analysis: Frame-by-frame timeline visualization
• Confidence Distribution: Statistical analysis of model scores

🎯 HOW TO USE:

1. Start the app: The Streamlit interface should open automatically
2. Navigate using the sidebar menu
3. For Single Video Analysis:
   - Click "Single Video Analysis"
   - Upload a video file (MP4, AVI, MOV, etc.)
   - Click "Run Deepfake Analysis"
   - Explore the comprehensive results dashboard

4. For Batch Analysis:
   - Click "Batch Analysis"
   - Upload multiple video files
   - Run batch processing
   - Compare results across videos

5. For Comparison:
   - Click "Compare Videos"
   - Select previously analyzed videos
   - View side-by-side comparisons

💡 TIPS:
- Larger videos take longer to process (1 frame per second extraction)
- Results are saved automatically for future comparison
- Use the export features to save analysis reports
- Check the Advanced Analytics for cross-video insights

🔧 TROUBLESHOOTING:
- If analysis fails, check that all model files are present
- Ensure video files are in supported formats
- For large videos, be patient during processing
- Check the terminal for detailed error messages
""")
    print("="*60)

def main():
    """Main demo launcher"""
    print("🎭 Deepfake Detection Studio - Demo Launcher")
    print("="*50)
    
    # Check current directory
    if not os.path.exists("inference.py"):
        print("❌ Error: Please run this script from the project root directory")
        print("   (where inference.py is located)")
        sys.exit(1)
    
    print("\n1. Checking dependencies...")
    missing_deps = check_dependencies()
    
    if missing_deps:
        print(f"\n❌ Missing dependencies: {missing_deps}")
        print("Installing missing packages...")
        
        if not install_streamlit_requirements():
            print("❌ Failed to install dependencies. Please install manually:")
            print("   pip install -r streamlit_requirements.txt")
            sys.exit(1)
    
    print("\n2. Checking model files...")
    missing_files = check_model_files()
    
    if missing_files:
        print(f"\n⚠️  Warning: Missing model files: {missing_files}")
        print("   Some features may not work without these files.")
        print("   You can still explore the UI with sample data.")
    
    print("\n3. Creating sample data for demo...")
    sample_dir = create_sample_data()
    
    print("\n4. Launching Streamlit app...")
    print_usage_instructions()
    
    try:
        # Launch Streamlit
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", "streamlit_app.py",
            "--server.port", "8501",
            "--server.address", "localhost"
        ])
    except KeyboardInterrupt:
        print("\n👋 Streamlit app stopped by user")
    except Exception as e:
        print(f"\n❌ Error launching Streamlit: {e}")
        print("\nTry running manually:")
        print("   streamlit run streamlit_app.py")

if __name__ == "__main__":
    main()
