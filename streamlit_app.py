#!/usr/bin/env python3
"""
Streamlit UI for Deepfake Detection Analysis

This application provides a beautiful interface for:
1. Running deepfake inference on videos
2. Visualizing results with comprehensive metrics
3. Comparing multiple videos
4. Exporting analysis reports
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import os
import subprocess
import glob
from pathlib import Path
import tempfile
import shutil
from datetime import datetime
import json
import base64
from typing import Tuple, Optional
import cv2
from PIL import Image

# Page configuration
st.set_page_config(
    page_title="Deepfake Detection Studio",
    page_icon="🎭",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
st.markdown("""
<style>
    .main-header {
        font-size: 3rem;
        font-weight: bold;
        text-align: center;
        background: linear-gradient(90deg, #ff6b6b, #4ecdc4);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        margin-bottom: 2rem;
    }
    
    .metric-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 1rem;
        border-radius: 10px;
        color: white;
        text-align: center;
        margin: 0.5rem 0;
    }
    
    .status-real {
        background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        padding: 0.5rem;
        border-radius: 5px;
        color: white;
        text-align: center;
    }
    
    .status-fake {
        background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
        padding: 0.5rem;
        border-radius: 5px;
        color: white;
        text-align: center;
    }
    
    .status-no-face {
        background: linear-gradient(135deg, #bdc3c7 0%, #2c3e50 100%);
        padding: 0.5rem;
        border-radius: 5px;
        color: white;
        text-align: center;
    }
    
    .sidebar-section {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 10px;
        margin: 1rem 0;
    }
</style>
""", unsafe_allow_html=True)

class DeepfakeAnalyzer:
    def __init__(self):
        self.results_dir = "./streamlit_results"
        os.makedirs(self.results_dir, exist_ok=True)
    
    def run_inference(self, video_path: str, progress_callback=None) -> Tuple[bool, str]:
        """Run inference on a video file"""
        try:
            output_dir = os.path.join(self.results_dir, f"analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
            
            cmd = [
                "python", "inference.py", 
                video_path,
                "--output_dir", output_dir
            ]
            
            if progress_callback:
                progress_callback("Starting inference...")
            
            # Run inference
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=1800)  # 30 min timeout
            
            if result.returncode == 0:
                return True, output_dir
            else:
                st.error(f"Inference failed: {result.stderr}")
                return False, ""
                
        except subprocess.TimeoutExpired:
            st.error("Inference timed out (30 minutes)")
            return False, ""
        except Exception as e:
            st.error(f"Error running inference: {e}")
            return False, ""
    
    def load_analysis_data(self, csv_path: str) -> Optional[pd.DataFrame]:
        """Load and validate CSV analysis data"""
        try:
            df = pd.read_csv(csv_path)
            
            # Validate required columns
            required_cols = ['frame_number', 'frame_filename', 'model_score', 'classification', 'prediction_label', 'timestamp']
            if not all(col in df.columns for col in required_cols):
                st.error(f"CSV missing required columns. Expected: {required_cols}")
                return None
            
            return df
        except Exception as e:
            st.error(f"Error loading CSV: {e}")
            return None
    
    def calculate_metrics(self, df: pd.DataFrame) -> dict:
        """Calculate comprehensive metrics from analysis data"""
        total_frames = len(df)
        
        # Basic counts
        real_frames = len(df[df['classification'] == 'Real'])
        fake_frames = len(df[df['classification'] == 'Fake'])
        no_face_frames = len(df[df['classification'] == 'No Face Detected'])
        
        # Percentages
        real_percentage = (real_frames / total_frames) * 100 if total_frames > 0 else 0
        fake_percentage = (fake_frames / total_frames) * 100 if total_frames > 0 else 0
        no_face_percentage = (no_face_frames / total_frames) * 100 if total_frames > 0 else 0
        
        # Face detection rate
        face_detection_rate = ((total_frames - no_face_frames) / total_frames) * 100 if total_frames > 0 else 0
        
        # Model confidence statistics (for frames with faces)
        face_frames = df[df['classification'] != 'No Face Detected']
        if len(face_frames) > 0:
            avg_confidence = face_frames['model_score'].mean()
            confidence_std = face_frames['model_score'].std()
            max_confidence = face_frames['model_score'].max()
            min_confidence = face_frames['model_score'].min()
        else:
            avg_confidence = confidence_std = max_confidence = min_confidence = 0
        
        # Video health score (higher = more real content)
        health_score = real_percentage
        
        # Fake segments analysis
        fake_segments = self.find_consecutive_segments(df, 'Fake')
        longest_fake_segment = max([seg[1] - seg[0] + 1 for seg in fake_segments]) if fake_segments else 0
        
        return {
            'total_frames': total_frames,
            'real_frames': real_frames,
            'fake_frames': fake_frames,
            'no_face_frames': no_face_frames,
            'real_percentage': real_percentage,
            'fake_percentage': fake_percentage,
            'no_face_percentage': no_face_percentage,
            'face_detection_rate': face_detection_rate,
            'health_score': health_score,
            'avg_confidence': avg_confidence,
            'confidence_std': confidence_std,
            'max_confidence': max_confidence,
            'min_confidence': min_confidence,
            'fake_segments': fake_segments,
            'longest_fake_segment': longest_fake_segment,
            'total_fake_segments': len(fake_segments)
        }
    
    def find_consecutive_segments(self, df: pd.DataFrame, classification: str) -> list[tuple[int, int]]:
        """Find consecutive segments of a specific classification"""
        target_frames = df[df['classification'] == classification]['timestamp'].tolist()
        
        if not target_frames:
            return []
        
        segments = []
        start = target_frames[0]
        end = target_frames[0]
        
        for i in range(1, len(target_frames)):
            if target_frames[i] == target_frames[i-1] + 1:
                end = target_frames[i]
            else:
                if end > start:  # Only include segments longer than 1 frame
                    segments.append((start, end))
                start = end = target_frames[i]
        
        # Add the last segment
        if end > start:
            segments.append((start, end))
        
        return segments

def main():
    # Header
    st.markdown('<h1 class="main-header">🎭 Deepfake Detection Studio</h1>', unsafe_allow_html=True)
    
    # Initialize analyzer
    analyzer = DeepfakeAnalyzer()
    
    # Sidebar
    with st.sidebar:
        st.markdown('<div class="sidebar-section">', unsafe_allow_html=True)
        st.header("🎯 Navigation")
        
        page = st.selectbox(
            "Choose Analysis Mode",
            ["🏠 Home", "📹 Single Video Analysis", "📊 Batch Analysis", "🔍 Compare Videos", "📈 Advanced Analytics"]
        )
        st.markdown('</div>', unsafe_allow_html=True)
        
        # Quick stats if results exist
        results_files = glob.glob(os.path.join(analyzer.results_dir, "*", "*_analysis.csv"))
        if results_files:
            st.markdown('<div class="sidebar-section">', unsafe_allow_html=True)
            st.header("📊 Quick Stats")
            st.metric("Total Analyses", len(results_files))
            
            # Load latest analysis for quick preview
            latest_file = max(results_files, key=os.path.getctime)
            latest_df = analyzer.load_analysis_data(latest_file)
            if latest_df is not None:
                metrics = analyzer.calculate_metrics(latest_df)
                st.metric("Latest Health Score", f"{metrics['health_score']:.1f}%")
            st.markdown('</div>', unsafe_allow_html=True)
    
    # Main content based on selected page
    if page == "🏠 Home":
        show_home_page()
    elif page == "📹 Single Video Analysis":
        show_single_video_analysis(analyzer)
    elif page == "📊 Batch Analysis":
        show_batch_analysis(analyzer)
    elif page == "🔍 Compare Videos":
        show_compare_videos(analyzer)
    elif page == "📈 Advanced Analytics":
        show_advanced_analytics(analyzer)

def show_home_page():
    """Display the home page with overview and instructions"""
    col1, col2, col3 = st.columns([1, 2, 1])
    
    with col2:
        st.markdown("""
        ## Welcome to Deepfake Detection Studio! 🎭
        
        This powerful tool helps you analyze videos for deepfake content using state-of-the-art AI models.
        
        ### 🚀 Features:
        - **Single Video Analysis**: Upload and analyze individual videos
        - **Batch Processing**: Analyze multiple videos at once
        - **Comparative Analysis**: Compare results across videos
        - **Advanced Metrics**: Detailed confidence scores and temporal analysis
        - **Beautiful Visualizations**: Interactive charts and graphs
        - **Export Reports**: Download analysis results
        
        ### 📊 Key Metrics:
        - **Health Score**: Overall authenticity percentage
        - **Temporal Analysis**: Frame-by-frame timeline
        - **Confidence Distribution**: Model certainty analysis
        - **Face Detection Rate**: Processing success rate
        - **Fake Segments**: Consecutive manipulation periods
        
        ### 🎯 Get Started:
        1. Choose "Single Video Analysis" from the sidebar
        2. Upload your video file
        3. Run the analysis
        4. Explore the results!
        """)
        
        # Sample metrics display
        st.markdown("### 📈 Sample Analysis Dashboard")
        
        # Create sample data for demonstration
        sample_data = pd.DataFrame({
            'timestamp': range(1, 31),
            'model_score': np.random.beta(2, 5, 30),  # Skewed towards lower values (more real)
            'classification': ['Real' if score < 0.5 else 'Fake' for score in np.random.beta(2, 5, 30)]
        })
        
        # Sample timeline chart
        fig = px.line(sample_data, x='timestamp', y='model_score', 
                     title="Sample: Model Confidence Over Time",
                     color_discrete_sequence=['#ff6b6b'])
        fig.add_hline(y=0.5, line_dash="dash", line_color="gray", 
                     annotation_text="Decision Threshold")
        fig.update_layout(height=300)
        st.plotly_chart(fig, use_container_width=True)

def show_single_video_analysis(analyzer):
    """Display single video analysis interface"""
    st.header("📹 Single Video Analysis")
    
    # File upload
    uploaded_file = st.file_uploader(
        "Choose a video file",
        type=['mp4', 'avi', 'mov', 'mkv', 'flv', 'wmv', 'webm'],
        help="Upload a video file to analyze for deepfake content"
    )
    
    if uploaded_file is not None:
        # Save uploaded file temporarily
        with tempfile.NamedTemporaryFile(delete=False, suffix=f".{uploaded_file.name.split('.')[-1]}") as tmp_file:
            tmp_file.write(uploaded_file.read())
            temp_video_path = tmp_file.name
        
        # Display video info
        col1, col2 = st.columns([2, 1])
        
        with col1:
            st.video(uploaded_file)
        
        with col2:
            st.markdown("### 📋 Video Information")
            st.write(f"**Filename:** {uploaded_file.name}")
            st.write(f"**Size:** {uploaded_file.size / (1024*1024):.1f} MB")
            
            # Get video properties
            try:
                cap = cv2.VideoCapture(temp_video_path)
                fps = cap.get(cv2.CAP_PROP_FPS)
                frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                duration = frame_count / fps if fps > 0 else 0
                width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                cap.release()
                
                st.write(f"**Duration:** {duration:.1f} seconds")
                st.write(f"**Resolution:** {width}x{height}")
                st.write(f"**FPS:** {fps:.1f}")
                st.write(f"**Estimated frames to analyze:** {int(duration)}")  # 1 FPS extraction
            except:
                st.write("Could not read video properties")
        
        # Analysis button
        if st.button("🚀 Run Deepfake Analysis", type="primary", use_container_width=True):
            with st.spinner("Running deepfake analysis... This may take several minutes."):
                progress_bar = st.progress(0)
                status_text = st.empty()
                
                # Run inference
                success, output_dir = analyzer.run_inference(temp_video_path)
                
                if success:
                    # Find the CSV file
                    csv_files = glob.glob(os.path.join(output_dir, "*_analysis.csv"))
                    if csv_files:
                        csv_path = csv_files[0]
                        df = analyzer.load_analysis_data(csv_path)
                        
                        if df is not None:
                            st.success("✅ Analysis completed successfully!")
                            
                            # Store results in session state for persistence
                            st.session_state['current_analysis'] = {
                                'df': df,
                                'video_name': uploaded_file.name,
                                'csv_path': csv_path
                            }
                            
                            # Display results
                            display_video_results(analyzer, df, uploaded_file.name)
                        else:
                            st.error("Failed to load analysis results")
                    else:
                        st.error("No analysis results found")
                else:
                    st.error("Analysis failed")
        
        # Clean up temporary file
        try:
            os.unlink(temp_video_path)
        except:
            pass
    
    # Display previous results if available
    if 'current_analysis' in st.session_state:
        st.markdown("---")
        st.header("📊 Previous Analysis Results")
        analysis = st.session_state['current_analysis']
        display_video_results(analyzer, analysis['df'], analysis['video_name'])

def display_video_results(analyzer, df: pd.DataFrame, video_name: str):
    """Display comprehensive analysis results for a video"""
    # Calculate metrics
    metrics = analyzer.calculate_metrics(df)
    
    # Header with video name
    st.markdown(f"## 📊 Analysis Results: {video_name}")
    
    # Key metrics cards
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        health_color = "🟢" if metrics['health_score'] > 70 else "🟡" if metrics['health_score'] > 30 else "🔴"
        st.markdown(f"""
        <div class="metric-card">
            <h3>{health_color} Health Score</h3>
            <h2>{metrics['health_score']:.1f}%</h2>
            <p>Authenticity Rating</p>
        </div>
        """, unsafe_allow_html=True)
    
    with col2:
        st.markdown(f"""
        <div class="metric-card">
            <h3>👥 Face Detection</h3>
            <h2>{metrics['face_detection_rate']:.1f}%</h2>
            <p>Successful Detection Rate</p>
        </div>
        """, unsafe_allow_html=True)
    
    with col3:
        st.markdown(f"""
        <div class="metric-card">
            <h3>🎯 Avg Confidence</h3>
            <h2>{metrics['avg_confidence']:.3f}</h2>
            <p>Model Certainty</p>
        </div>
        """, unsafe_allow_html=True)
    
    with col4:
        st.markdown(f"""
        <div class="metric-card">
            <h3>⚠️ Fake Segments</h3>
            <h2>{metrics['total_fake_segments']}</h2>
            <p>Suspicious Periods</p>
        </div>
        """, unsafe_allow_html=True)

    # Detailed breakdown
    st.markdown("### 📈 Detailed Breakdown")

    col1, col2 = st.columns(2)

    with col1:
        # Classification pie chart
        classification_counts = df['classification'].value_counts()
        colors = ['#38ef7d', '#ff4b2b', '#bdc3c7']  # Green, Red, Gray

        fig_pie = go.Figure(data=[go.Pie(
            labels=classification_counts.index,
            values=classification_counts.values,
            marker_colors=colors,
            textinfo='label+percent',
            textfont_size=12
        )])
        fig_pie.update_layout(
            title="Frame Classification Distribution",
            height=400,
            showlegend=True
        )
        st.plotly_chart(fig_pie, use_container_width=True)

    with col2:
        # Confidence distribution histogram
        face_frames = df[df['classification'] != 'No Face Detected']
        if len(face_frames) > 0:
            fig_hist = px.histogram(
                face_frames,
                x='model_score',
                nbins=20,
                title="Model Confidence Distribution",
                color_discrete_sequence=['#667eea']
            )
            fig_hist.add_vline(x=0.5, line_dash="dash", line_color="red",
                              annotation_text="Decision Threshold")
            fig_hist.update_layout(height=400)
            st.plotly_chart(fig_hist, use_container_width=True)
        else:
            st.info("No frames with detected faces for confidence analysis")

    # Timeline analysis
    st.markdown("### ⏱️ Timeline Analysis")

    # Create timeline plot
    fig_timeline = make_subplots(
        rows=2, cols=1,
        subplot_titles=("Model Confidence Over Time", "Classification Timeline"),
        vertical_spacing=0.1,
        row_heights=[0.6, 0.4]
    )

    # Confidence timeline
    face_frames = df[df['classification'] != 'No Face Detected']
    if len(face_frames) > 0:
        fig_timeline.add_trace(
            go.Scatter(
                x=face_frames['timestamp'],
                y=face_frames['model_score'],
                mode='lines+markers',
                name='Confidence Score',
                line=dict(color='#667eea', width=2),
                marker=dict(size=4)
            ),
            row=1, col=1
        )

        # Add threshold line
        fig_timeline.add_hline(
            y=0.5, line_dash="dash", line_color="red",
            annotation_text="Fake Threshold",
            row=1, col=1
        )

    # Classification timeline (color-coded bars)
    colors_map = {'Real': '#38ef7d', 'Fake': '#ff4b2b', 'No Face Detected': '#bdc3c7'}
    for classification in df['classification'].unique():
        class_data = df[df['classification'] == classification]
        fig_timeline.add_trace(
            go.Bar(
                x=class_data['timestamp'],
                y=[1] * len(class_data),
                name=classification,
                marker_color=colors_map[classification],
                showlegend=True
            ),
            row=2, col=1
        )

    fig_timeline.update_layout(
        height=600,
        title_text="Temporal Analysis Dashboard",
        showlegend=True
    )
    fig_timeline.update_xaxes(title_text="Time (seconds)", row=2, col=1)
    fig_timeline.update_yaxes(title_text="Confidence Score", row=1, col=1)
    fig_timeline.update_yaxes(title_text="Classification", row=2, col=1)

    st.plotly_chart(fig_timeline, use_container_width=True)

    # Fake segments analysis
    if metrics['fake_segments']:
        st.markdown("### ⚠️ Suspicious Segments Analysis")

        segments_data = []
        for i, (start, end) in enumerate(metrics['fake_segments']):
            duration = end - start + 1
            segments_data.append({
                'Segment': f"Segment {i+1}",
                'Start Time (s)': start,
                'End Time (s)': end,
                'Duration (s)': duration,
                'Frames': duration
            })

        segments_df = pd.DataFrame(segments_data)
        st.dataframe(segments_df, use_container_width=True)

        # Segments visualization
        fig_segments = px.timeline(
            segments_df,
            x_start='Start Time (s)',
            x_end='End Time (s)',
            y='Segment',
            title="Fake Content Timeline",
            color_discrete_sequence=['#ff4b2b']
        )
        fig_segments.update_layout(height=300)
        st.plotly_chart(fig_segments, use_container_width=True)

    # Statistical summary
    st.markdown("### 📊 Statistical Summary")

    col1, col2, col3 = st.columns(3)

    with col1:
        st.markdown("#### Frame Counts")
        st.write(f"**Total Frames:** {metrics['total_frames']}")
        st.write(f"**Real Frames:** {metrics['real_frames']} ({metrics['real_percentage']:.1f}%)")
        st.write(f"**Fake Frames:** {metrics['fake_frames']} ({metrics['fake_percentage']:.1f}%)")
        st.write(f"**No Face:** {metrics['no_face_frames']} ({metrics['no_face_percentage']:.1f}%)")

    with col2:
        st.markdown("#### Confidence Statistics")
        if metrics['avg_confidence'] > 0:
            st.write(f"**Mean Score:** {metrics['avg_confidence']:.4f}")
            st.write(f"**Std Deviation:** {metrics['confidence_std']:.4f}")
            st.write(f"**Min Score:** {metrics['min_confidence']:.4f}")
            st.write(f"**Max Score:** {metrics['max_confidence']:.4f}")
        else:
            st.write("No confidence data available")

    with col3:
        st.markdown("#### Segment Analysis")
        st.write(f"**Fake Segments:** {metrics['total_fake_segments']}")
        st.write(f"**Longest Fake:** {metrics['longest_fake_segment']} seconds")
        st.write(f"**Face Detection:** {metrics['face_detection_rate']:.1f}%")

        # Overall assessment
        if metrics['health_score'] > 80:
            st.success("🟢 **Assessment:** Likely Authentic")
        elif metrics['health_score'] > 50:
            st.warning("🟡 **Assessment:** Mixed Content")
        else:
            st.error("🔴 **Assessment:** Likely Manipulated")

    # Export options
    st.markdown("### 💾 Export Results")

    col1, col2, col3 = st.columns(3)

    with col1:
        # Download CSV
        csv_data = df.to_csv(index=False)
        st.download_button(
            label="📄 Download CSV Data",
            data=csv_data,
            file_name=f"{video_name}_analysis.csv",
            mime="text/csv"
        )

    with col2:
        # Download JSON report
        report_data = {
            'video_name': video_name,
            'analysis_timestamp': datetime.now().isoformat(),
            'metrics': metrics,
            'summary': {
                'health_score': metrics['health_score'],
                'recommendation': 'Authentic' if metrics['health_score'] > 80 else 'Mixed' if metrics['health_score'] > 50 else 'Suspicious'
            }
        }

        json_data = json.dumps(report_data, indent=2, default=str)
        st.download_button(
            label="📋 Download JSON Report",
            data=json_data,
            file_name=f"{video_name}_report.json",
            mime="application/json"
        )

    with col3:
        # View raw data
        if st.button("👁️ View Raw Data"):
            st.dataframe(df, use_container_width=True)

def show_batch_analysis(analyzer):
    """Display batch analysis interface"""
    st.header("📊 Batch Analysis")
    st.info("Upload multiple videos or select a directory for batch processing")

    # Multiple file upload
    uploaded_files = st.file_uploader(
        "Choose video files",
        type=['mp4', 'avi', 'mov', 'mkv', 'flv', 'wmv', 'webm'],
        accept_multiple_files=True,
        help="Upload multiple video files for batch analysis"
    )

    if uploaded_files:
        st.write(f"Selected {len(uploaded_files)} files for analysis")

        # Display file list
        for i, file in enumerate(uploaded_files):
            st.write(f"{i+1}. {file.name} ({file.size / (1024*1024):.1f} MB)")

        if st.button("🚀 Run Batch Analysis", type="primary"):
            batch_results = []

            progress_bar = st.progress(0)
            status_text = st.empty()

            for i, uploaded_file in enumerate(uploaded_files):
                status_text.text(f"Processing {uploaded_file.name}...")
                progress_bar.progress((i + 1) / len(uploaded_files))

                # Save file temporarily
                with tempfile.NamedTemporaryFile(delete=False, suffix=f".{uploaded_file.name.split('.')[-1]}") as tmp_file:
                    tmp_file.write(uploaded_file.read())
                    temp_video_path = tmp_file.name

                # Run analysis
                success, output_dir = analyzer.run_inference(temp_video_path)

                if success:
                    csv_files = glob.glob(os.path.join(output_dir, "*_analysis.csv"))
                    if csv_files:
                        df = analyzer.load_analysis_data(csv_files[0])
                        if df is not None:
                            metrics = analyzer.calculate_metrics(df)
                            batch_results.append({
                                'video_name': uploaded_file.name,
                                'metrics': metrics,
                                'df': df
                            })

                # Clean up
                try:
                    os.unlink(temp_video_path)
                except:
                    pass

            # Display batch results
            if batch_results:
                st.success(f"✅ Completed analysis of {len(batch_results)} videos")
                display_batch_results(batch_results)
            else:
                st.error("No successful analyses completed")

def display_batch_results(batch_results: list[dict]):
    """Display comprehensive batch analysis results"""
    st.markdown("## 📊 Batch Analysis Results")

    # Summary table
    summary_data = []
    for result in batch_results:
        metrics = result['metrics']
        summary_data.append({
            'Video': result['video_name'],
            'Health Score (%)': f"{metrics['health_score']:.1f}",
            'Total Frames': metrics['total_frames'],
            'Fake Frames': metrics['fake_frames'],
            'Face Detection (%)': f"{metrics['face_detection_rate']:.1f}",
            'Avg Confidence': f"{metrics['avg_confidence']:.3f}",
            'Fake Segments': metrics['total_fake_segments']
        })

    summary_df = pd.DataFrame(summary_data)
    st.dataframe(summary_df, use_container_width=True)

    # Comparative visualizations
    st.markdown("### 📈 Comparative Analysis")

    col1, col2 = st.columns(2)

    with col1:
        # Health scores comparison
        health_scores = [result['metrics']['health_score'] for result in batch_results]
        video_names = [result['video_name'] for result in batch_results]

        fig_health = px.bar(
            x=video_names,
            y=health_scores,
            title="Health Score Comparison",
            color=health_scores,
            color_continuous_scale=['red', 'yellow', 'green']
        )
        fig_health.update_layout(height=400)
        fig_health.update_xaxes(tickangle=45)
        st.plotly_chart(fig_health, use_container_width=True)

    with col2:
        # Face detection rates
        face_rates = [result['metrics']['face_detection_rate'] for result in batch_results]

        fig_face = px.bar(
            x=video_names,
            y=face_rates,
            title="Face Detection Rate Comparison",
            color_discrete_sequence=['#667eea']
        )
        fig_face.update_layout(height=400)
        fig_face.update_xaxes(tickangle=45)
        st.plotly_chart(fig_face, use_container_width=True)

def show_compare_videos(analyzer):
    """Display video comparison interface"""
    st.header("🔍 Compare Videos")
    st.info("Select existing analysis results to compare")

    # Find available analyses
    results_files = glob.glob(os.path.join(analyzer.results_dir, "*", "*_analysis.csv"))

    if not results_files:
        st.warning("No analysis results found. Please run some analyses first.")
        return

    # Extract video names from file paths
    available_analyses = {}
    for file_path in results_files:
        video_name = os.path.basename(file_path).replace('_analysis.csv', '')
        available_analyses[video_name] = file_path

    # Multi-select for comparison
    selected_videos = st.multiselect(
        "Select videos to compare",
        options=list(available_analyses.keys()),
        help="Choose 2 or more videos for comparison"
    )

    if len(selected_videos) >= 2:
        # Load selected analyses
        comparison_data = []
        for video_name in selected_videos:
            df = analyzer.load_analysis_data(available_analyses[video_name])
            if df is not None:
                metrics = analyzer.calculate_metrics(df)
                comparison_data.append({
                    'video_name': video_name,
                    'metrics': metrics,
                    'df': df
                })

        if comparison_data:
            display_comparison_results(comparison_data)

def display_comparison_results(comparison_data: list[dict]):
    """Display detailed comparison between videos"""
    st.markdown("## 🔍 Video Comparison Results")

    # Side-by-side metrics
    cols = st.columns(len(comparison_data))

    for i, data in enumerate(comparison_data):
        with cols[i]:
            metrics = data['metrics']
            st.markdown(f"### {data['video_name']}")

            # Health score with color coding
            st.metric("Health Score", f"{metrics['health_score']:.1f}%", delta=None)
            st.metric("Face Detection", f"{metrics['face_detection_rate']:.1f}%")
            st.metric("Avg Confidence", f"{metrics['avg_confidence']:.3f}")
            st.metric("Fake Segments", metrics['total_fake_segments'])

def show_advanced_analytics(analyzer):
    """Display advanced analytics and insights"""
    st.header("📈 Advanced Analytics")
    st.info("Deep dive into analysis patterns and trends")

    # Find all available analyses
    results_files = glob.glob(os.path.join(analyzer.results_dir, "*", "*_analysis.csv"))

    if not results_files:
        st.warning("No analysis results found. Please run some analyses first.")
        return

    # Load all data for advanced analytics
    all_data = []
    for file_path in results_files:
        video_name = os.path.basename(file_path).replace('_analysis.csv', '')
        df = analyzer.load_analysis_data(file_path)
        if df is not None:
            df['video_name'] = video_name
            all_data.append(df)

    if all_data:
        combined_df = pd.concat(all_data, ignore_index=True)
        display_advanced_insights(combined_df)

def display_advanced_insights(combined_df: pd.DataFrame):
    """Display advanced insights from all analyses"""
    st.markdown("## 🧠 Advanced Insights")

    # Overall statistics
    total_videos = combined_df['video_name'].nunique()
    total_frames = len(combined_df)
    overall_fake_rate = (len(combined_df[combined_df['classification'] == 'Fake']) / total_frames) * 100

    col1, col2, col3 = st.columns(3)
    with col1:
        st.metric("Total Videos Analyzed", total_videos)
    with col2:
        st.metric("Total Frames Processed", total_frames)
    with col3:
        st.metric("Overall Fake Rate", f"{overall_fake_rate:.1f}%")

    # Advanced visualizations
    st.markdown("### 📊 Cross-Video Analysis")

    # Confidence distribution across all videos
    face_frames = combined_df[combined_df['classification'] != 'No Face Detected']
    if len(face_frames) > 0:
        fig_violin = px.violin(
            face_frames,
            y='model_score',
            x='video_name',
            title="Confidence Score Distribution by Video",
            box=True
        )
        fig_violin.update_layout(height=500)
        fig_violin.update_xaxes(tickangle=45)
        st.plotly_chart(fig_violin, use_container_width=True)

if __name__ == "__main__":
    main()
