#!/usr/bin/env python3
"""
Complete usage example for the Streamlit Deepfake Detection UI

This script demonstrates how to:
1. Launch the Streamlit app
2. Use the different analysis modes
3. Interpret the results
4. Export data for further analysis
"""

import subprocess
import sys
import os
import time
import webbrowser
from pathlib import Path

def print_banner():
    """Print welcome banner"""
    print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║           🎭 DEEPFAKE DETECTION STUDIO                       ║
    ║                                                              ║
    ║              Beautiful Streamlit Interface                   ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """)

def check_prerequisites():
    """Check if all prerequisites are met"""
    print("🔍 Checking prerequisites...")
    
    # Check if we're in the right directory
    required_files = ["streamlit_app.py", "inference.py", "run_streamlit_demo.py"]
    missing_files = [f for f in required_files if not os.path.exists(f)]
    
    if missing_files:
        print(f"❌ Missing files: {missing_files}")
        print("Please run this script from the project root directory.")
        return False
    
    # Check if sample data exists
    sample_dir = "./streamlit_results/sample_demo"
    if not os.path.exists(sample_dir):
        print("📊 Creating sample data for demonstration...")
        try:
            from run_streamlit_demo import create_sample_data
            create_sample_data()
            print("✅ Sample data created successfully")
        except Exception as e:
            print(f"⚠️  Could not create sample data: {e}")
    else:
        print("✅ Sample data already exists")
    
    print("✅ All prerequisites met!")
    return True

def show_feature_overview():
    """Show overview of features"""
    print("""
    🌟 FEATURE OVERVIEW:
    
    📹 SINGLE VIDEO ANALYSIS
    ├── Upload individual video files (MP4, AVI, MOV, etc.)
    ├── Real-time processing with progress indicators
    ├── Comprehensive analysis dashboard
    ├── Interactive timeline visualizations
    ├── Confidence score distributions
    ├── Fake segment detection and analysis
    └── Export options (CSV, JSON reports)
    
    📊 BATCH ANALYSIS
    ├── Process multiple videos simultaneously
    ├── Comparative analysis across video collections
    ├── Batch statistics and health score rankings
    ├── Cross-video pattern identification
    └── Aggregate insights and trends
    
    🔍 VIDEO COMPARISON
    ├── Side-by-side analysis of multiple videos
    ├── Relative performance metrics
    ├── Difference highlighting
    └── Trend analysis across video sets
    
    📈 ADVANCED ANALYTICS
    ├── Cross-video statistical analysis
    ├── Confidence distribution patterns
    ├── Historical trend analysis
    └── Pattern recognition insights
    """)

def show_metrics_guide():
    """Show guide to understanding metrics"""
    print("""
    📊 METRICS INTERPRETATION GUIDE:
    
    🎯 HEALTH SCORE (0-100%)
    ├── 🟢 80-100%: Likely authentic content
    ├── 🟡 50-79%:  Mixed content, requires review
    └── 🔴 0-49%:   Likely manipulated content
    
    👥 FACE DETECTION RATE
    ├── High rate (>90%): Good video quality for analysis
    ├── Medium rate (70-90%): Acceptable for analysis
    └── Low rate (<70%): Poor quality, results may be unreliable
    
    🎯 MODEL CONFIDENCE (0.0-1.0)
    ├── 0.0-0.3: Strong indication of real content
    ├── 0.3-0.7: Uncertain region, requires careful analysis
    └── 0.7-1.0: Strong indication of fake content
    
    ⚠️ FAKE SEGMENTS
    ├── Number of consecutive suspicious periods
    ├── Longer segments indicate sustained manipulation
    └── Multiple short segments may indicate editing artifacts
    
    📈 TIMELINE PATTERNS
    ├── Consistent low scores: Likely authentic
    ├── Sudden spikes: Potential manipulation points
    ├── Sustained high scores: Likely fake segments
    └── Oscillating patterns: Mixed or partially manipulated
    """)

def show_usage_workflow():
    """Show step-by-step usage workflow"""
    print("""
    🚀 STEP-BY-STEP USAGE WORKFLOW:
    
    1️⃣ GETTING STARTED
    ├── Launch: python run_streamlit_demo.py
    ├── Browser opens automatically to http://localhost:8501
    ├── Navigate using the sidebar menu
    └── Start with "Home" for overview
    
    2️⃣ SINGLE VIDEO ANALYSIS
    ├── Click "Single Video Analysis" in sidebar
    ├── Upload video file (drag & drop or browse)
    ├── Review video information (duration, resolution, etc.)
    ├── Click "Run Deepfake Analysis" button
    ├── Wait for processing (1-2 seconds per frame)
    ├── Explore comprehensive results dashboard
    ├── Download CSV/JSON reports if needed
    └── Results are automatically saved for comparison
    
    3️⃣ BATCH PROCESSING
    ├── Click "Batch Analysis" in sidebar
    ├── Upload multiple video files
    ├── Review file list and total size
    ├── Click "Run Batch Analysis"
    ├── Monitor progress for each video
    ├── Compare results in summary table
    └── Analyze patterns across video collection
    
    4️⃣ COMPARISON ANALYSIS
    ├── Click "Compare Videos" in sidebar
    ├── Select 2+ previously analyzed videos
    ├── View side-by-side metrics
    ├── Identify performance differences
    └── Understand relative characteristics
    
    5️⃣ ADVANCED INSIGHTS
    ├── Click "Advanced Analytics" in sidebar
    ├── View cross-video statistical analysis
    ├── Explore confidence distribution patterns
    ├── Identify trends and patterns
    └── Generate insights for research/analysis
    """)

def launch_streamlit_app():
    """Launch the Streamlit app"""
    print("\n🚀 Launching Streamlit Deepfake Detection Studio...")
    print("📱 The app will open in your default browser")
    print("🌐 URL: http://localhost:8501")
    print("\n⏹️  Press Ctrl+C to stop the server when done")
    print("=" * 60)
    
    try:
        # Launch Streamlit
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", "streamlit_app.py",
            "--server.port", "8501",
            "--server.address", "localhost",
            "--server.headless", "false"
        ])
    except KeyboardInterrupt:
        print("\n\n👋 Streamlit app stopped by user")
        print("Thank you for using Deepfake Detection Studio!")
    except Exception as e:
        print(f"\n❌ Error launching Streamlit: {e}")
        print("\n🔧 Troubleshooting:")
        print("1. Ensure Streamlit is installed: pip install streamlit")
        print("2. Try running manually: streamlit run streamlit_app.py")
        print("3. Check if port 8501 is available")

def show_demo_data_info():
    """Show information about demo data"""
    print("""
    📊 DEMO DATA INFORMATION:
    
    The app includes sample analysis data for demonstration:
    
    🎬 sample_real_video_analysis.csv
    ├── Simulates mostly authentic content
    ├── Health Score: ~85-95%
    ├── Few fake segments
    └── Good for testing "real" video analysis
    
    🎭 sample_mixed_video_analysis.csv
    ├── Simulates mixed authentic/fake content
    ├── Health Score: ~50-70%
    ├── Multiple fake segments
    └── Good for testing complex analysis
    
    🎪 sample_fake_video_analysis.csv
    ├── Simulates mostly manipulated content
    ├── Health Score: ~10-30%
    ├── Sustained fake segments
    └── Good for testing "fake" video detection
    
    💡 TIP: Use "Compare Videos" to see differences between these samples!
    """)

def main():
    """Main function to run the complete example"""
    print_banner()
    
    if not check_prerequisites():
        sys.exit(1)
    
    print("\n" + "=" * 60)
    print("📚 COMPREHENSIVE USAGE GUIDE")
    print("=" * 60)
    
    # Show all guides
    show_feature_overview()
    show_metrics_guide()
    show_usage_workflow()
    show_demo_data_info()
    
    print("\n" + "=" * 60)
    print("🎯 READY TO LAUNCH!")
    print("=" * 60)
    
    # Ask user if they want to launch
    response = input("\n🚀 Launch Streamlit app now? (y/n): ").lower().strip()
    
    if response in ['y', 'yes', '']:
        launch_streamlit_app()
    else:
        print("\n📝 To launch manually later, run:")
        print("   python run_streamlit_demo.py")
        print("   OR")
        print("   streamlit run streamlit_app.py")
        print("\n👋 Happy analyzing!")

if __name__ == "__main__":
    main()
