#!/usr/bin/env python3
"""
Test script for the Streamlit Deepfake Detection UI

This script tests the core functionality without launching the full UI.
"""

import pandas as pd
import numpy as np
import os
import tempfile
from datetime import datetime

# Import our Streamlit app components
from streamlit_app import DeepfakeAnalyzer

def create_test_data():
    """Create test CSV data for validation"""
    # Create sample analysis data
    frames = 20
    np.random.seed(42)  # For reproducible results
    
    test_data = {
        'frame_number': range(1, frames + 1),
        'frame_filename': [f'frame_{i:04d}.jpg' for i in range(1, frames + 1)],
        'model_score': np.random.beta(2, 5, frames),  # Skewed towards lower values
        'prediction_label': [0 if score < 0.5 else 1 for score in np.random.beta(2, 5, frames)],
        'timestamp': range(1, frames + 1)
    }
    
    # Add classification based on model_score
    test_data['classification'] = ['Real' if score < 0.5 else 'Fake' for score in test_data['model_score']]
    
    # Add some "No Face Detected" frames
    test_data['classification'][5] = 'No Face Detected'
    test_data['classification'][15] = 'No Face Detected'
    test_data['model_score'][5] = 0.0
    test_data['model_score'][15] = 0.0
    test_data['prediction_label'][5] = -1
    test_data['prediction_label'][15] = -1
    
    return pd.DataFrame(test_data)

def test_analyzer_functionality():
    """Test the DeepfakeAnalyzer class functionality"""
    print("🧪 Testing DeepfakeAnalyzer functionality...")
    
    # Initialize analyzer
    analyzer = DeepfakeAnalyzer()
    print("✅ Analyzer initialized successfully")
    
    # Create test data
    test_df = create_test_data()
    print(f"✅ Test data created: {len(test_df)} frames")
    
    # Test CSV loading functionality
    with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as tmp_file:
        test_df.to_csv(tmp_file.name, index=False)
        tmp_csv_path = tmp_file.name
    
    loaded_df = analyzer.load_analysis_data(tmp_csv_path)
    if loaded_df is not None and len(loaded_df) == len(test_df):
        print("✅ CSV loading works correctly")
    else:
        print("❌ CSV loading failed")
        return False
    
    # Test metrics calculation
    metrics = analyzer.calculate_metrics(loaded_df)
    
    expected_metrics = [
        'total_frames', 'real_frames', 'fake_frames', 'no_face_frames',
        'real_percentage', 'fake_percentage', 'no_face_percentage',
        'face_detection_rate', 'health_score', 'avg_confidence',
        'confidence_std', 'max_confidence', 'min_confidence',
        'fake_segments', 'longest_fake_segment', 'total_fake_segments'
    ]
    
    missing_metrics = [metric for metric in expected_metrics if metric not in metrics]
    if not missing_metrics:
        print("✅ All expected metrics calculated")
        print(f"   Health Score: {metrics['health_score']:.1f}%")
        print(f"   Face Detection Rate: {metrics['face_detection_rate']:.1f}%")
        print(f"   Total Frames: {metrics['total_frames']}")
        print(f"   Real Frames: {metrics['real_frames']}")
        print(f"   Fake Frames: {metrics['fake_frames']}")
        print(f"   No Face Frames: {metrics['no_face_frames']}")
    else:
        print(f"❌ Missing metrics: {missing_metrics}")
        return False
    
    # Test consecutive segments detection
    fake_segments = analyzer.find_consecutive_segments(loaded_df, 'Fake')
    print(f"✅ Fake segments detection: {len(fake_segments)} segments found")
    
    # Clean up
    os.unlink(tmp_csv_path)
    
    return True

def test_data_validation():
    """Test data validation and error handling"""
    print("\n🧪 Testing data validation...")
    
    analyzer = DeepfakeAnalyzer()
    
    # Test with invalid CSV (missing columns)
    invalid_data = pd.DataFrame({
        'frame_number': [1, 2, 3],
        'invalid_column': ['a', 'b', 'c']
    })
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as tmp_file:
        invalid_data.to_csv(tmp_file.name, index=False)
        tmp_csv_path = tmp_file.name
    
    loaded_df = analyzer.load_analysis_data(tmp_csv_path)
    if loaded_df is None:
        print("✅ Invalid CSV properly rejected")
    else:
        print("❌ Invalid CSV should have been rejected")
        return False
    
    # Clean up
    os.unlink(tmp_csv_path)
    
    return True

def test_sample_data_creation():
    """Test the sample data creation for demo"""
    print("\n🧪 Testing sample data creation...")
    
    # Test the demo data creation
    try:
        from run_streamlit_demo import create_sample_data
        sample_dir = create_sample_data()
        
        # Check if files were created
        expected_files = [
            "sample_real_video_analysis.csv",
            "sample_mixed_video_analysis.csv", 
            "sample_fake_video_analysis.csv"
        ]
        
        all_files_exist = True
        for filename in expected_files:
            file_path = os.path.join(sample_dir, filename)
            if os.path.exists(file_path):
                # Validate the CSV structure
                df = pd.read_csv(file_path)
                required_cols = ['frame_number', 'frame_filename', 'model_score', 'classification', 'prediction_label', 'timestamp']
                if all(col in df.columns for col in required_cols):
                    print(f"✅ {filename} created and validated")
                else:
                    print(f"❌ {filename} missing required columns")
                    all_files_exist = False
            else:
                print(f"❌ {filename} not created")
                all_files_exist = False
        
        return all_files_exist
        
    except Exception as e:
        print(f"❌ Sample data creation failed: {e}")
        return False

def run_comprehensive_test():
    """Run all tests and provide summary"""
    print("🎭 DEEPFAKE DETECTION STUDIO - COMPREHENSIVE TEST")
    print("=" * 60)
    
    tests = [
        ("Core Analyzer Functionality", test_analyzer_functionality),
        ("Data Validation", test_data_validation),
        ("Sample Data Creation", test_sample_data_creation)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 Running: {test_name}")
        print("-" * 40)
        
        try:
            if test_func():
                print(f"✅ {test_name} PASSED")
                passed_tests += 1
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} FAILED with exception: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 TEST SUMMARY: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        print("🎉 ALL TESTS PASSED! The Streamlit app is ready to use.")
        print("\n🚀 To launch the app, run:")
        print("   python run_streamlit_demo.py")
        print("   OR")
        print("   streamlit run streamlit_app.py")
    else:
        print("⚠️  Some tests failed. Please check the issues above.")
    
    print("=" * 60)
    
    return passed_tests == total_tests

if __name__ == "__main__":
    success = run_comprehensive_test()
    exit(0 if success else 1)
